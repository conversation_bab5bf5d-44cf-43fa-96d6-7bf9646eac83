package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.entity.abm.CustomerTagsRela;
import com.ce.scrm.customer.dao.service.abm.CustomerTagsRelaService;
import com.ce.scrm.customer.service.business.entity.dto.CustomerESBusinessDto;
import com.ce.scrm.customer.service.business.entity.dto.CustomerESTagBusinessDto;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/***
 * 监听客户库变更写入elasticsearch
 * <AUTHOR>
 * @date 2024/7/16 19:10
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_CUSTOMER_TOPIC, consumerGroup = ServiceConstant.MqConstant.Group.CDP_CUSTOMER_ELASTICSEARCH_GROUP, consumeThreadMax = 5)
public class CustomerConsumer extends AbstractEventConsumer<CustomerBinLog> implements RocketMQListener<MessageExt> {

    @Autowired
    private RestHighLevelClient esClient;

    @Resource
    CustomerTagsRelaService customerTagsRelaService;

    private final String indexName = "es_crm_customer";

    /**
     * UPDATE 操作 需要监听 scrm_customer.customer 发生变更的字段.    key为数据库字段名，value为神策用户属性名称
     */
    private static HashMap<String, String> eventFieldsReflectUpdate = new HashMap<>();

    static {
        eventFieldsReflectUpdate.put("customer_type", "customer_type");
        eventFieldsReflectUpdate.put("customer_name", "customer_name");
        eventFieldsReflectUpdate.put("certificate_code", "certificate_code");
    }


    @Override
    /**
     * <AUTHOR>
     * @date 2025/7/10 18:08:53
     * @return java.util.List<com.ce.scrm.customer.async.mq.consumer.CustomerBinLog>
     * @desc 处理customer binlog写入到es
     */
    public List<CustomerBinLog> dealEvent(JSONObject beforeJson, JSONObject afterJson, String type) {
        try {
            // Convert JSON to Customer object
            Customer customer = afterJson.toJavaObject(Customer.class);

            // Convert Customer to CustomerESBusinessDto
            CustomerESBusinessDto dto = convertToESDto(customer);

            if("insert".equalsIgnoreCase(type) || "update".equalsIgnoreCase(type)){
                QueryWrapper<CustomerTagsRela> wrapper = new QueryWrapper<>();
                wrapper.eq("customer_id", customer.getCustomerId());
                wrapper.select("tag_code", "(SELECT weight FROM customer_tags WHERE customer_tags.tag_code = customer_tags_rela.tag_code limit 1) as score");

                List<Map<String, Object>> list = customerTagsRelaService.listMaps(wrapper);

                BigDecimal totalScore = BigDecimal.ZERO;
                List<CustomerESTagBusinessDto> tagList = new ArrayList<>();
                for (Map<String, Object> map : list) {
                    String tagCode = (String) map.get("tag_code");
                    if (StrUtil.isBlank(tagCode)) {
                        continue;
                    }
                    totalScore = totalScore.add(new BigDecimal(String.valueOf(map.get("score"))));
                    CustomerESTagBusinessDto tag = new CustomerESTagBusinessDto();
                    tag.setTagCode(tagCode);
                    tagList.add(tag);
                }
                if (CollUtil.isNotEmpty(tagList)) {
                    dto.setTagList(tagList);
                    dto.setTagScore(totalScore);
                } else {
                    dto.setTagList(Collections.emptyList());
                    dto.setTagScore(BigDecimal.ZERO);
                }
            }

            // Perform ES operation based on type
            for (int i = 0; i < 3; i++) {
                try{
                    switch (type.toLowerCase()) {
                        case "insert":
                            IndexRequest indexRequest = new IndexRequest(indexName)
                                    .source(JSON.toJSONString(dto), XContentType.JSON).id(dto.getCustomerId());
                            esClient.index(indexRequest, RequestOptions.DEFAULT);
                            break;
                        case "update":
                            UpdateRequest updateRequest = new UpdateRequest(indexName,  dto.getCustomerId())
                                    .doc(JSON.toJSONString(dto), XContentType.JSON).upsert(JSON.toJSONString(dto), XContentType.JSON);
                            esClient.update(updateRequest, RequestOptions.DEFAULT);
                            break;
                        case "delete":
                            DeleteRequest deleteRequest = new DeleteRequest(indexName, dto.getCustomerId());
                            esClient.delete(deleteRequest, RequestOptions.DEFAULT);
                            break;
                        default:
                            log.error("更新客户ES失败,无效的类型:{}", type);
                    }
                }catch (ElasticsearchStatusException e) {
                    if (e.status().getStatus() == 409 || e.getMessage().contains("version_conflict_engine_exception")) {
                        //版本冲突丢弃
                        log.info("版本冲突丢弃,exception:{}, afterJson:{}", e.getMessage(), JSON.toJSONString(afterJson));
                        continue;
                    } else {
                        log.error("更新客户ES失败,exception:{}, afterJson:{}", e.getMessage(), JSON.toJSONString(afterJson));
                        break;
                    }
                } catch (Exception e) {
                    log.error("更新客户ES失败,exception:{}, afterJson:{}", e.getMessage(), JSON.toJSONString(afterJson));
                    break;
                }

            }
        } catch (ElasticsearchStatusException e) {
            if (e.status().getStatus() == 409 || e.getMessage().contains("version_conflict_engine_exception")) {
                //版本冲突丢弃
                log.info("版本冲突丢弃,exception:{}, afterJson:{}", e.getMessage(), JSON.toJSONString(afterJson));
            } else {
                log.error("更新客户ES失败,exception:{}, afterJson:{}", e.getMessage(), JSON.toJSONString(afterJson));
            }
        } catch (Exception e) {
            log.error("更新客户ES失败,exception:{}, afterJson:{}", e.getMessage(), JSON.toJSONString(afterJson));
        }
        return null;
    }

    private CustomerESBusinessDto convertToESDto(Customer customer) {
        CustomerESBusinessDto dto = new CustomerESBusinessDto();
        BeanUtil.copyProperties(customer, dto);
        dto.setPid(customer.getSourceDataId());
        dto.setStage(customer.getPresentStage());
        dto.setSalerId(customer.getProtectSalerId());
        dto.setDeptId(customer.getProtectBussdeptId());
        dto.setSubId(customer.getProtectSubcompanyId());
        dto.setAreaId(customer.getProtectAreaId());
        dto.setBuId(customer.getProtectBuId());
        dto.setStatus(customer.getProtectStatus());
        //如果为null,空,空格等，则默认值为2
        dto.setTradeProductType(StrUtil.blankToDefault(customer.getProtectCustType(),"2"));
        dto.setProtectTime(customer.getProtectProtectTime());
        dto.setProtectEndTime(customer.getProtectProtectendTime());
        dto.setProtectDay(customer.getCdpProtectDay());
        dto.setClockCountMonth(customer.getCdpCurrentMonthClockCount());
        dto.setClockCount(customer.getCdpClockCount());
        dto.setFirstPaymentTime(customer.getCdpFirstPaymentTime());
        dto.setTagMenhuLower(customer.getTagMenhuLowver());
        dto.setRegistrationOrg(customer.getRegistrationAuthority());
        return dto;
    }

    @Override
    public void onMessage(MessageExt messageExt) {
        super.dealBinlogMqMessage(messageExt);
    }
}