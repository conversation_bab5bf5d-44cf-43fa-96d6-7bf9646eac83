package com.ce.scrm.customer.service.constant;

import com.ce.scrm.customer.cache.constant.CacheConstant;
import com.ce.scrm.customer.util.constant.UtilConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

/**
 * 业务常量
 * <AUTHOR>
 * @date 2023/4/6 16:21
 * @version 1.0.0
 **/
@Slf4j
public class ServiceConstant {

    /**
     * 精确查询标记
     */
    public final static ThreadLocal<Boolean> EXACT_QUERY_FLAG = new ThreadLocal<>();

    /**
     * 职位枚举映射
     */
    public static Map<String, String> POSITION_MAPPING = new HashMap<>(150);

    static {
        String s;
        try {
            Resource resource = new ClassPathResource("position_mapping.txt");
            InputStream is = resource.getInputStream();
            InputStreamReader isr = new InputStreamReader(is);
            BufferedReader br = new BufferedReader(isr);
            while ((s = br.readLine()) != null) {
                String[] strings = s.split(UtilConstant.DATA_VALUE_SEPARATOR);
                String value = "";
                if (strings.length > 1) {
                    value = strings[1];
                }
                POSITION_MAPPING.put(strings[0], value);
            }
        } catch (IOException e) {
            log.error("position_mapping.txt 文件未找到");
            throw new RuntimeException("position_mapping.txt 文件未找到");
        }
        if (log.isInfoEnabled()) {
            log.info("POSITION_MAPPING加载成功,数据量为:{}", POSITION_MAPPING.size());
        }
    }


    public static class CacheKey {
        /**
         * demo缓存
         */
        public final static String DEMO_CACHE = CacheConstant.CACHE_PREFIX + CacheConstant.CACHE_KEY_SEPARATOR + "demo";
    }

    /**
     * 锁key
     */
    public static class LockKey {
        /**
         * demo锁前缀
         */
        public final static String DEMO_LOCK = "demo";
    }

    /**
     * 定时任务相关常量
     * <AUTHOR>
     * @date 2023/4/6 16:22
     * @version 1.0.0
     **/
    public static class JobConstant {

        /**
         * 任务类型常量
         */
        public static class JobDescConstant {
            /**
             * 示例描述
             */
            public static final String DEMO_DESC = "当前任务是一个示例";
        }

        /**
         * 任务处理器
         */
        public static class JobHandlerConstant {
            /**
             * 清洗客户数据
             */
            public static final String CLEAN_CUSTOMER_JOB_HANDLER = "CLEAN_CUSTOMER_JOB_HANDLER";
            /**
             * 停止清洗客户数据
             */
            public static final String STOP_CLEAN_CUSTOMER_JOB_HANDLER = "STOP_CLEAN_CUSTOMER_JOB_HANDLER";
            /**
             * 迁移无效客户数据
             */
            public static final String TRANSFER_INVALID_CUSTOMER_JOB_HANDLER = "TRANSFER_INVALID_CUSTOMER_JOB_HANDLER";
            /**
             * 刷新客户名称和uncid
             */
            public static final String REFRESH_CUSTOMER_NAME_JOB_HANDLER = "REFRESH_CUSTOMER_NAME_JOB_HANDLER";

            public static final String SYNC_CUSTOMER_KA_FLAG_JOB_HANDLER = "SYNC_CUSTOMER_KA_FLAG_JOB_HANDLER";
            /**
             * 客户id放入MQ，用于将待流转客户insert到待流转表
             */
            public static final String CUSTOMER_ID_TO_MQ_FOR_CIRCULATION = "CUSTOMER_ID_TO_MQ_FOR_CIRCULATION";

            /**
             * 更新legal_person_flag是否法人
             */
            public static final String REFRESH_LEGAL_PERSON_FLAG_JOB_HANDLER = "REFRESH_LEGAL_PERSON_FLAG_JOB_HANDLER";

            /**
             * 从业务平台每天抓取分享内容数据 缓存到 redis 中
             */
            public static final String SCRM_CACHE_SHARE_DATA_JOB_HANDLER = "SCRM_CACHE_SHARE_DATA_JOB_HANDLER";

            /**
             * 批量发送customerId到MQ 业务使用：（流转）
             */
            public static final String SCRM_BATCH_SEND_CUSTOMER_ID_JOB_HANDLER = "SCRM_BATCH_SEND_CUSTOMER_ID_JOB_HANDLER";

	        /**
	         * 添加跨境ABM营销活动
	         */
	        public static final String SCRM_CUSTOMER_MARKETING_ACTIVITIES_JOB_HANDLER = "SCRM_CUSTOMER_MARKETING_ACTIVITIES_JOB_HANDLER";
        }
    }

    /**
     * mq常量池
     * <AUTHOR>
     * @date 2023/4/6 16:23
     * @version 1.0.0
     **/
    public static class MqConstant {

        /**
         * 主题标签分隔符
         */
        public final static String TOPIC_TAG_SEPARATOR = ":";

        /**
         * 主题
         */
        public static class Topic {
            /**
             * 客户相关topic
             */
            public final static String CUSTOMER_TOPIC = "CUSTOMER_TOPIC";

            /**
             * 刷新客户名称和unCid topic
             */
            public final static String CUSTOMER_SCRM_REFRESH_CUSTOMER_NAME_TOPIC = "CUSTOMER_SCRM_REFRESH_CUSTOMER_NAME_TOPIC";
            /**
             * 搜客宝相关标签同步
             */
            public final static String CDP_CUS_FLAG_TOPIC = "CDP_CUS_FLAG_TOPIC";
            /**
             * 门户实例标签同步
             */
            public final static String CDP_CUSTOMER_TAG_UPDATE_TOPIC = "CDP_CUSTOMER_TAG_UPDATE_TOPIC";


            /**
             * 客户信息放入MQ，用于去搜客宝es查询flag值
             */
            public final static String CDP_CUS_INFO_FOR_FLAG_TOPIC = "CDP_CUS_INFO_FOR_FLAG_TOPIC";

            /**
             * cdp 同步数据 到scrm
             */
            public final static String CDP_CESUPPORT_SCRM_CUST_LABEL_TOPIC = "CDP_CESUPPORT_SCRM_CUST_LABEL_TOPIC";
            public final static String CDP_SYNC_CUST_RETAIN_LOST_LABEL_TOPIC = "CDP_SYNC_CUST_RETAIN_LOST_LABEL_TOPIC";
            public final static String CDP_SYNC_CUST_QUALIFIED_LABEL_TOPIC = "CDP_SYNC_CUST_QUALIFIED_LABEL_TOPIC";


            /**
             * cdp 同步报价到客户库
             */
            public final static String CDP_SYNC_BJKH_SCRM_TOPIC = "CDP_SYNC_BJKH_SCRM_TOPIC";
            /**
             * 客户id放入MQ，用于将待流转客户insert到待流转表
             */
            public final static String CUST_ID_FOR_CIRCULATION_TOPIC = "CUST_ID_FOR_CIRCULATION_TOPIC";

            /**
             * 联系人绑定微信unionId
             */
            public final static String CDP_CONTACTPERSON_BIND_UNIONID_TOPIC = "CDP_CONTACTPERSON_BIND_UNIONID_TOPIC";

            /**
             * 导入客户
             */
            public final static String SCRM_CUSTOMER_IMPORT_TOPIC = "SCRM_CUSTOMER_IMPORT_TOPIC";

            /**
             * customerId 批量发送 （流转在用）
             */
            public final static String SCRM_CUSTOMER_ID_BATCH_SEND_TOPIC = "SCRM_CUSTOMER_ID_BATCH_SEND_TOPIC";
            /**
             * 绑定联系人
             */
            public static final String CUST_BIND_EXTERNALCONTACT_TOPIC = "CUST_BIND_EXTERNALCONTACT_TOPIC";
            public static final String CUST_BIND_EXTERNALCONTACT_HISTORY_TOPIC = "CUST_BIND_EXTERNALCONTACT_HISTORY_TOPIC";
            /**
             * customer_bind_wx 表 binlog
             */
            public static final String CDP_CUSTOMER_BIND_WX_TOPIC = "CDP_CUSTOMER_BIND_WX_TOPIC";


            /**
	         * 创建营销活动后，接受拉取的客户信息
	         */
            public final static String CDP_CUSTOMER_SEGMENT_CUSTOMER_INFO_TOPIC = "CDP_CUSTOMER_SEGMENT_CUSTOMER_INFO_TOPIC";

	        /**
	         * 创建营销活动后，发消息给CDP，获取分群中的客户数据
	         */
            public final static String CUSTOMER_CDP_ACTIVITY_SEGMENT_ID_TOPIC = "CUSTOMER_CDP_ACTIVITY_SEGMENT_ID_TOPIC";
            public final static String CDP_CUSTOMER_TOPIC = "CDP_CUSTOMER_TOPIC";

            public final static String CDP_CUSTOMER_TAGS_RELA_TOPIC = "CDP_CUSTOMER_TAGS_RELA_TOPIC";

            /**
             * 保护关系表binlog
             */
            public final static String CDP_CM_CUST_PROTECT_TOPIC = "CDP_CM_CUST_PROTECT_TOPIC";

            /**
             * 接收 客户跟进事件-客户拜访和电联记录表 cm_cust_visit_log 的binlog 的topic
             */
            public static final String CDP_CM_CUST_VISIT_LOG_TOPIC = "CDP_CM_CUST_VISIT_LOG_TOPIC";


        }

        /**
         * 标签
         */
        public static class Tag {
            /**
             * 检查有效客户标签
             */
            public final static String CHECK_VALID_CUSTOMER_TAG = "CHECK_VALID_CUSTOMER_TAG";
            /**
             * 迁移无效客户标签
             */
            public final static String TRANSFER_INVALID_CUSTOMER_TAG = "TRANSFER_INVALID_CUSTOMER_TAG";
        }

        /**
         * 订阅组常量
         */
        public static class Group {
            /**
             * 检查有效客户消费组
             */
            public final static String CHECK_VALID_CUSTOMER_GROUP = "CHECK_VALID_CUSTOMER_GROUP";
            /**
             * 迁移无效客户消费组
             */
            public final static String TRANSFER_INVALID_CUSTOMER_GROUP = "TRANSFER_INVALID_CUSTOMER_GROUP";

            /**
             * 刷新客户名称和unCid group
             */
            public final static String CUSTOMER_SCRM_REFRESH_CUSTOMER_NAME_GROUP = "CUSTOMER_SCRM_REFRESH_CUSTOMER_NAME_GROUP";

            /**
             * 搜客宝相关标签同步消费组
             */
            public final static String CDP_CUS_FLAG_CUSTOMER_GROUP = "CDP_CUS_FLAG_CUSTOMER_GROUP";

            /**
             * 门户实例标签同步消费组
             */
            public final static String CDP_CUSTOMER_TAG_UPDATE_GROUP = "CDP_CUSTOMER_TAG_UPDATE_GROUP";

            /**
             * cdp 同步数据 到scrm 消费组
             */
            public final static String CDP_SYNC_DATA_TO_SCRM_GROUP = "CDP_SYNC_DATA_TO_SCRM_GROUP";
            public final static String CDP_SYNC_CUST_RETAIN_LOST_LABEL_GROUP = "CDP_SYNC_CUST_RETAIN_LOST_LABEL_GROUP";

            /**
             * cdp 同步报价客户 到scrm
             */
            public final static String CDP_SYNC_BJKH_SCRM_GROUP = "CDP_SYNC_BJKH_SCRM_GROUP";

            public final static String CDP_SYNC_CUST_QUALIFIED_LABEL_GROUP = "CDP_SYNC_CUST_QUALIFIED_LABEL_GROUP";

            public final static String SCRM_CUSTOMER_IMPORT_GROUP = "SCRM_CUSTOMER_IMPORT_GROUP";


            public static final String CUST_BIND_EXTERNALCONTACT_GROUP = "CUST_BIND_EXTERNALCONTACT_GROUP";
            public static final String CUST_BIND_EXTERNALCONTACT_HISTORY_GROUP = "CUST_BIND_EXTERNALCONTACT_HISTORY_GROUP";

            /**
	         * 创建营销活动后，接受拉取的客户信息
	         */
            public final static String CDP_CUSTOMER_SEGMENT_CUSTOMER_INFO_GROUP = "CDP_CUSTOMER_SEGMENT_CUSTOMER_INFO_GROUP";

	        /**
	         * 创建营销活动后，发消息给CDP，获取分群中的客户数据
	         */
	        public final static String CUSTOMER_CDP_ACTIVITY_SEGMENT_ID_GROUP = "CUSTOMER_CDP_ACTIVITY_SEGMENT_ID_GROUP";
            public final static String CDP_CUSTOMER_ELASTICSEARCH_GROUP = "CDP_CUSTOMER_ELASTICSEARCH_GROUP";
            /**
             * 保护关系表 绑定标识维护
             */
            public final static String SCRM_PROTECT_BIND_FLAG_GROUP = "SCRM_PROTECT_BIND_FLAG_GROUP";

            public final static String CDP_ES_CUSTOMER_TAGS_RELA_GROUP = "CDP_ES_CUSTOMER_TAGS_RELA_GROUP";

            /**
             * 保护关系表变更处理customer表数据
             */
            public final static String PROTECT_TO_CUSTOMER_GROUP = "PROTECT_TO_CUSTOMER_GROUP";

            /**
             * cm_cust_visit_log 跟进记录表变更处理customer表数据
             */
            public final static String VISIT_LOG_TO_CUSTOMER_GROUP = "VISIT_LOG_TO_CUSTOMER_GROUP";

        }

        /**
         * 顺序消费常量
         */
        public static class OrderlyConsumer {
            /**
             * 保证顺序消费的同一字符串
             */
            public final static String ORDER_FLAG = "SCRM-CUSTOMER";
        }

        /**
         * 延迟消息级别
         * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
         */
        public static class Level {

            public static final int ONE_SECOND = 1;

            public static final int FIVE_SECOND = 2;

            public static final int TEN_SECOND = 3;

            public static final int THIRTY_SECOND = 4;

            public static final int ONE_MINUTE = 5;

            public static final int TWO_MINUTE = 6;

            public static final int THREE_MINUTE = 7;

            public static final int FOUR_MINUTE = 8;

            public static final int FIVE_MINUTE = 9;

            public static final int SIX_MINUTE = 10;

            public static final int SEVEN_MINUTE = 11;

            public static final int EIGHT_MINUTE = 12;

            public static final int NINE_MINUTE = 13;

            public static final int TEN_MINUTE = 14;

            public static final int TWENTY_MINUTE = 15;

            public static final int THIRTY_MINUTE = 16;

            public static final int ONE_HOUR = 17;

            public static final int TWO_HOUR = 18;

        }
    }
}
