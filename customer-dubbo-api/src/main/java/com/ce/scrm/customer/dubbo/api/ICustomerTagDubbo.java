package com.ce.scrm.customer.dubbo.api;

import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerTagRelaCreateDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerTagDubboView;

import java.util.List;
import java.util.Map;

/**
 * 跨境ABM 客户标签相关dubbo接口
 * <AUTHOR>
 * @date 2025/7/09 16:54
 * @version 1.0.0
 */
public interface ICustomerTagDubbo {

	/**
	 * 导入客户和标签后：增加客户标签关联关系、修改权重积分
	 * @param tagRelaCreateDubboDto 增加客户标签关联关系
	 * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<Boolean>
	 * <AUTHOR>
	 * @date 2025/7/15 15:39
	 **/
	DubboResult<Boolean> addCustomerTagRela(CustomerTagRelaCreateDubboDto tagRelaCreateDubboDto);


	/**
	 * 根据客户id列表获取客户标签列表，按积分从大到小排序
	 * @param customerIdList 客户id列表
	 * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.Map < java.lang.String, java.util.List<com.ce.scrm.customer.dubbo.entity.view.abm.CustomerTagDubboView>>>
	 */
	DubboResult<Map<String, List<CustomerTagDubboView>>> customerTagList(List<String> customerIdList);

	/**
	 * 根据标签分类id查询对应的标签列表
	 * @param tagCategoryId 标签分类id
	 * @return 当前分类id下面的标签列表
	 */
	DubboResult<List<CustomerTagDubboView>> getTagsByCategoryId(List<Long> tagCategoryIds);


}